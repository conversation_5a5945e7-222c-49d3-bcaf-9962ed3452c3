import { FxElement, html } from '/fx.js';
import { $styles } from './main.x.js';
import '../icon/icon.js';
import '../tabs/tabs.js';
import '../form/form.js';

// const registerServiceWorker = async () => {
//     try {
//         if (!(location.protocol === 'https:' || location.hostname === 'localhost' || location.hostname === '127.0.0.1')) { console.log('ServiceWorker requires HTTPS or localhost'); return }
//         console.log('Checking ServiceWorker support...');
//         if (!('serviceWorker' in navigator)) { console.log('ServiceWorker is not supported'); return }
//         console.log('Registering ServiceWorker...');
//         const registration = await navigator.serviceWorker.register('./sw.js', { scope: './' })
//         console.log('ServiceWorker registration successful:', registration);
//     } catch (error) { console.log('ServiceWorker registration failed:', error) }
// }
// if (typeof window !== 'undefined') { registerServiceWorker() }

export class FxMain extends FxElement {
    static properties = {
        isReady: { type: Boolean },
        menuSize: { type: Number, default: 42 },
        heightT: { type: Number, default: 34 },
        heightF: { type: Number, default: 24 },
        hide: { type: String, default: '' },
        fixedL: { type: String, default: '' },
        fixedB: { type: String, default: '' },
        fixedR: { type: String, default: '' },
        overlay: { type: String, default: 'r' },
        activeL: { type: Boolean, default: true, save: true },
        activeR: { type: Boolean, default: false, save: true },
        activeB: { type: Boolean, default: false, save: true },
        activeM: { type: Boolean, default: true, save: true },
        hideAllLeft: { type: Boolean, default: false, save: true },
        wl: { type: Number, default: 280, save: true },
        minl: { type: Number, default: 150 },
        maxl: { type: String, default: '' },
        wr: { type: Number, default: 280, save: true },
        minr: { type: Number, default: 150 },
        maxr: { type: String, default: '' },
        hb: { type: Number, default: 280, save: true },
        minb: { type: Number, default: 66 },
        maxb: { type: String, default: '' },
        resizing: { type: Object },
        editMode: { type: Boolean, default: false },
        needSave: { type: Boolean, default: false },
        changesMap: { type: Object },
        activeMT: { type: String, default: 'files', save: true },
        activeMb: { type: String },
        tabs: { type: Array, default: [] },
        tabsWidth: { type: Number, default: 120 },
        theme: { type: String, default: '', save: true },
        btnsMT: { type: Array, default: [] },
        btnsMB: { type: Array, default: [] },
        btnsTL: { type: Array, default: [] },
        btnsTR: { type: Array, default: [] },
        base: { type: Object },
    }

    get hideT() { return this.hide?.includes('t') }
    get hideF() { return this.hide?.includes('f') }
    get hideB() { return this.hide?.includes('b') }
    get hideL() { return this.hide?.includes('l') }
    get hideR() { return this.hide?.includes('r') }
    get hideM() { return this.hide?.includes('m') }
    get hideUser() { return this.hide?.includes('u') }
    get hideSets() { return this.hide?.includes('s') }
    get hideReopen() { return this.hide?.includes('o') }
    get hideSave() { return this.hide?.includes('a') }
    get hideCount() { return this.hide?.includes('c') }
    get widthL() { return this.activeL ? (this.wl < this.minl ? this.minl + 'px' : this.wl > this.maxl ? this.maxl + 'px' : this.wl + 'px') : 0 }
    get widthR() { return this.activeR ? (this.wr < this.minr ? this.minr + 'px' : this.wr > this.maxr ? this.maxr + 'px' : this.wr + 'px') : 0 }
    get overlayL() { return this.overlay?.includes('l') }
    get overlayR() { return this.overlay?.includes('r') }
    get fxTabs() { return this.$qs('fx-tabs') }
    get activeTab() { return this.fxTabs?.activeTab || 0 }
    get activeTabName() { return this.fxTabs?.activeTabName || '' }
    get activeTabId() { return this.fxTabs?.activeTabId || '' }
    get activeTabGroup() { return this.fxTabs?.activeTabGroup || '' }
    get frmInfo() { return this.$qs('#info') }
    get frmUser() { return this.$qs('#user') }
    get changesMap() { return this.base?.changesMap }

    firstUpdated() {
        super.firstUpdated();
        this.async(() => {
            this._updateSaves();
            this.switchTheme(this.theme);
            this.maxl = this.maxl || this.offsetWidth - this.minl - this.menuSize;
            this.wl = this.checkSize('l', this.wl);
            this.wl = this.wl < this.minl ? this.minl : this.wl;
            this.maxr = this.maxr || this.offsetWidth - this.minr - this.menuSize;
            this.wr = this.checkSize('r', this.wr);
            this.wr = this.wr < this.minr ? this.minr : this.wr;
            this.maxb = this.maxb || this.offsetHeight - this.minb - this.heightF - this.heightT;
            this.hb = this.checkSize('b', this.hb);
            //alert(this.wl + ' ' + this.wr + ' ' + this.hb);
            this.async(() => {
                document.body.style.opacity = 1;
                this.isReady = true;
            }, 100)
        }, 20)
    }
    updated(changedProps) {
        super.updated(changedProps);
        this.updateCustomProperties();
    }

    switchTheme(theme) {
        theme ||= this.theme = this.theme === 'dark' ? 'light' : 'dark';
        FX.switchTheme(theme);
    }
    checkSize(panel, val) {
        let type = panel === 'b' ? 'h' : 'w';
        type += panel;
        this[type] = val < this['min' + panel] ? this['min' + panel] : val > this['max' + panel] ? this['max' + panel] : val;
        // console.log(this[type]);
        return this[type];
    }
    updateCustomProperties() {
        if (!this.isConnected) return;
        const menu = this.hideM ? 0 : this.menuSize || 42;
        this.style.setProperty('--header-height', `${this.hideT ? 0 : this.heightT}px`);
        this.style.setProperty('--footer-height', `${this.hideF ? 0 : this.heightF}px`);
        this.style.setProperty('--bottom-height', `${this.fixedB || this.hideB || !this.activeB ? 0 : this.hb}px`);
        this.style.setProperty('--menu-width', `${menu}px`);
        this.style.setProperty('--left-panel-margin-left', `${this.overlayL ? -menu : 0}px`);
        this.style.setProperty('--left-panel-width', `${this.fixedL || this.wl}px`);
        this.style.setProperty('--left-panel-min-width', `${this.activeL ? this.fixedL || (this.minl + 'px') : 0}`);
        this.style.setProperty('--left-panel-max-width', `${this.activeL ? this.fixedL || (this.maxl + 'px') : 0}`);
        this.style.setProperty('--right-panel-width', `${this.fixedR || this.wr}px`);
        this.style.setProperty('--right-panel-min-width', `${this.activeR ? this.fixedR || (this.minr + 'px') : 0}`);
        this.style.setProperty('--right-panel-max-width', `${this.activeR ? this.fixedR || (this.maxr + 'px') : 0}`);
        this.style.setProperty('--user-select', this.resizing ? 'none' : '');
        this.style.setProperty('--opacity', this.isReady ? '1' : '0');
    }
    togglePanel(e) {
        const id = e.target.id;
        if (id === 'toggle-left')
            this.activeL = !this.activeL;
        else if (id === 'toggle-right')
            this.activeR = !this.activeR;
        else if (id === 'toggle-bottom')
            this.activeB = !this.activeB;
        else if (id === 'toggle-menu')
            this.activeM = !this.activeM;
    }
    _btnClick() {}
    btnClick(e) {
        let id = e.target.id;
        const _id = this._btnClick(e, id);
        // console.log(id, _id);
        // id = _id || id;
        if (e.target.className.includes('btns-mt')) {
            if (this.activeMT === id) {
                this.activeL = !this.activeL;
            } else {
                this.activeMT = id;
                this.activeL = true;
            }
            this.$update();
            return;
        }
        if (id === 'btn-account') {
            FX.showForm('user', { slot: 'main' }, { modal: '320, 460', opacity: 0.4, label: 'User' });
            return;
        }
        if (id === 'btn-reload') {
            document.location.reload();
            return;
        }
        if (id === 'logo') {
            if (this.activeL || this.activeM)
                this.hideAllLeft = !this.hideAllLeft;
            if (!this.activeL) this.activeM = true;
            if (this.hideAllLeft) this.activeR = false;
            return;
        }
        this.fire('main-btn-click', { e, id });
        // if (id === 'btn-refresh') {
        //     this.fire('mainRefresh');
        //     return;
        // }
    }
    showChangesMap() {
        let res = {};
        if (this.base?.fxChanged || this.base?.fxHasDeleted) {
            res[this.base.fxItems._id] = this.base.fxItems;
        }
        (Array.from(this.changesMap?.values()).sort((i, i2) => i.ulid > i2.ulid ? 1 : -1)).map( i => {
            let doc = i.doc;
            res[doc._id] = doc;
        })
        FX.showPG( { io: res, show: '', expert: true }, { label: 'changesMap', hideBottom: true }, 'changesMap');
    }

    static styles = [$styles]

    get _btnsTL() {
        let count = (this.changesMap?.size || 0) + (this.base?.fxChanged || this.base?.fxHasDeleted ? 1 : 0);
        return [
            html`<img id="logo" class="p2 ml8 pointer" src="/favicon.png" alt="logo" style="width: 24px; height: 24px; opacity: 1" @click=${e => this.btnClick(e)}>`,
            html`            
                <div class="block relative ml6" ?hidden=${this.hideSave || !(this.hideAllLeft || this.hideM || !this.activeM)}>
                    <div class="horizontal center br" style="width: 12px; height: 12px; border-radius: 50%; z-index: 9999; background: white; padding: 2px;color: red; font-size: 10px; position: absolute; top: 0px; right: -8px; cursor: pointer;" ?hidden=${!count || this.hideCount} @click=${this.showChangesMap}>${count}</div>
                    <fx-icon class="box m2" br="circle" id="btn-save" name="codicon:save-all:28" scale=.65 an="btn" back=${this.needSave ? 'yellow' : ''} style="opacity: ${this.needSave ? 1 : .2};" @click=${this.btnClick}
                        fill=${this.needSave ? 'oklch(0.7 0.35 20)' : 'var(--fx-color-dark)'} ?disabled=${!this.needSave}
                    ></fx-icon>
                </div>
            `,
            html`<fx-icon id="btn-reload" class="ml5" name="codicon:refresh:24" scale=.8 an="btn" rt="90" @click=${e => this.btnClick(e)}></fx-icon>`,
            html`<fx-icon id="btn-refresh" class="ml5" name="codicon:issue-reopened:24" scale=.8 an="btn" ?hidden=${this.hideReopen} @click=${e => this.btnClick(e)}></fx-icon>`
        ]
    }
    get _btnsTR() {
        return [
            html`<fx-icon id="btn-theme" class="ml2 mr2" url="ion:sunny-outline:24" id="theme" title="change theme" an="btn" @click=${e => this.switchTheme('')}></fx-icon>`,
            html`<fx-icon id="toggle-menu" class="mr2" name="codicon:layout:24" scale=.67 an="btn" @click=${this.togglePanel} ?hidden=${this.hideM} style="opacity: ${this.activeM ? 1 : .3};"></fx-icon>`,
            html`<fx-icon id="toggle-left" class="mr2" name=${this.activeL ? 'codicon:layout-sidebar-left:24' : 'codicon:layout-sidebar-left-off:24'} scale=.67 an="btn" @click=${this.togglePanel} ?hidden=${this.hideL}></fx-icon>`,
            html`<fx-icon id="toggle-bottom" class="mr2" name=${this.activeB ? 'codicon:layout-panel:24' : 'codicon:layout-panel-off:24'} scale=.67 an="btn" @click=${this.togglePanel} ?hidden=${this.hideB}></fx-icon>`,
            html`<fx-icon id="toggle-right" class="mr3" name=${this.activeR ? 'codicon:layout-sidebar-right:24' : 'codicon:layout-sidebar-right-off:24'} scale=.67 an="btn" @click=${this.togglePanel} ?hidden=${this.hideR}></fx-icon>`
        ]
    }
    get _top() {
        if (this.hideT) return null;
        return html`
            <header class="horizontal flex w100 center header">
                ${this._btnsTL.map(btn => btn)}
                <div class="horizontal flex center">
                    <slot name="top"></slot>
                </div>
                ${this._btnsTR.map(btn => btn)}
            </header>
        `
    }
    get _left() {
        if (this.hideL || this.hideAllLeft) return null;
        return html`
            <div class="side left-side ${this.overlayL ? 'overlay-left' : ''}">
                <aside id="left-panel" class="left-panel ${this.activeL ? 'active' : ''}">
                    <slot .name=${this.activeMT || 'left'}></slot>
                </aside>
                ${!this.fixedL &&this.activeL ? html`
                    <div id="left-splitter" class="v-splitter splitter" 
                        @pointerdown=${this.#resizeHandler.start} 
                        style="background: ${this.resizing?.isLeft ? 'var(--fx-color-selected)' : ''};">
                    </div>
                ` : ''}
            </div>
        `
    }
    get _right() {
        if (this.hideR) return null;
        return html`
            <div class="side right-side ${this.overlayR ? 'overlay-right' : ''}">
                ${!this.fixedR &&this.activeR ? html`
                    <div id="right-splitter" class="v-splitter splitter" 
                        @pointerdown=${this.#resizeHandler.start}
                        style="background: ${this.resizing?.isRight ? 'var(--fx-color-selected)' : ''};">
                    </div>
                ` : ''}
                <aside id="right-panel" class="right-panel ${this.activeR ? 'active' : ''}">
                    <slot name="right"></slot>
                </aside>
            </div>
        `
    }
    get _main() {
        return html`
            <div class="main vertical flex overflow-y h100 w100 relative">
                <fx-tabs .tabs=${this.tabs} .width=${this.tabsWidth} style="display: inline-grid; min-height: 24px!important;"></fx-tabs>
                <main class="vertical flex overflow-y" style="position: relative;">
                    ${this.tabs?.length || this.tabs?.tabs?.length ? html`
                        <!-- <div class="tab-content vertical flex overflow-y" style="padding: 16px;"> -->
                            ${this.tabs[this.activeTab]?.content}
                            <slot .name=${this.activeTabId}></slot>
                        <!-- </div> -->
                    ` : html`
                        <slot name="main"></slot>
                    `}
                    ${this._bottom}
                </main>
            </div>
        `;
    }
    get _btnsMT() {
        let count = (this.changesMap?.size || 0) + (this.base?.fxChanged || this.base?.fxHasDeleted ? 1 : 0);
        return html`
            <div class="block relative" ?hidden=${this.hideSave}>
                <div class="horizontal center br" style="width: 12px; height: 12px; border-radius: 50%; z-index: 9999; background: white; padding: 2px;color: red; font-size: 10px; position: absolute; top: 0px; right: 0px; cursor: pointer;" ?hidden=${!count || this.hideCount} @click=${this.showChangesMap}>${count}</div>
                <fx-icon class="box m2" br="circle" id="btn-save" name="codicon:save-all:38" scale=.65 an="btn" back=${this.needSave ? 'yellow' : ''} style="opacity: ${this.needSave ? 1 : .2};" @click=${this.btnClick}
                    fill=${this.needSave ? 'oklch(0.7 0.35 20)' : 'var(--fx-color-dark)'} ?disabled=${!this.needSave}
                ></fx-icon>
            </div>
            ${(this.btnsMT || []).map(i => { return html`
                <fx-icon class="btns-mt" id=${i.id} name=${i.name} scale=${i.scale || .65} an="btn" @click=${e => this.btnClick(e)} ?selected=${this.activeMT === i.id}></fx-icon>
            `})}
                
        `
    }
    get _btnsMB() {
        return html`
            ${(this.btnsMB || []).map(i => { return html`
                <fx-icon class="btns-mt" id=${i.id} name=${i.name} scale=${i.scale || .65} an="btn" @click=${e => this.btnClick(e)} ?selected=${this.activeMT === i.id}></fx-icon>
            `})}
            <fx-icon id="btn-account" icon="codicon:account:42" scale=.55 an="btn" @click=${this.btnClick} ?hidden=${this.hideUser}></fx-icon>
            <fx-icon id="btn-settings" icon="clarity:settings-line:42" an="btn" scale=.65 @click=${this.btnClick} ?hidden=${this.hideSets}></fx-icon>
        `
    }
    get _menu() {
        if (this.hideM || !this.activeM || this.hideAllLeft) return null;
        return html`
            <div class="menu vertical flex">
                ${this._btnsMT}
                <slot name="menu-top"></slot>
                <div class="flex"></div>
                <slot name="menu-bottom"></slot>
                ${this._btnsMB}
            </div>
        `
    }
    get _bottom() {
        if (this.hideB || !this.activeB) return null;
        return html` 
            <div class="vertical w100 bottom">
                ${!this.fixedB && this.activeB ? html`
                    <div id="h-splitter" class="h-splitter splitter w100" 
                        @pointerdown=${this.#resizeHandler.start}
                        style="background: ${this.resizing?.isBottom ? 'var(--fx-color-selected)' : ''}; z-index: 99">
                    </div>
                ` : ''}
                <slot name="bottom"></slot>
            </div>
        `
    }
    get _footer() {
        if (this.hideF) return null;
        return html`
            <footer class="horizontal flex center w100 footer brt">
                <slot name="footer"></slot>
            </footer>
        `
    }
    render() {
        return html`
            <div class="vertical w100 h100 flex relative overflow-h">
                ${this._top}
                <div class="horizontal flex w100 h100 relative overflow-h">
                    ${this._menu}
                    ${this._left}
                    ${this._main}
                    ${this._right}
                </div>
                ${this._footer}
            </div>
        `
    }

    #resizeHandler = {
        start: (e) => {
            const isLeft = e.target.id === 'left-splitter',
                isRight = e.target.id === 'right-splitter',
                isBottom = e.target.id === 'h-splitter';
            this.resizing = {
                isLeft,
                isRight,
                isBottom,
                startX: e.clientX,
                startWidth: isLeft ? this.$qs('.left-panel')?.offsetWidth : this.$qs('.right-panel')?.offsetWidth,
                startY: e.clientY,
                startHeight: this.$qs('.bottom')?.offsetHeight
            }
            document.addEventListener('pointermove', this.#resizeHandler.move);
            document.addEventListener('pointerup', this.#resizeHandler.stop);
        },
        move: (e) => {
            if (!this.resizing) return;
            const { isLeft, isBottom, startX, startWidth, startY, startHeight } = this.resizing;
            let delta;
            if (isBottom) {
                delta = startY - e.clientY;
                delta = startHeight + delta;
                this.hb = this.checkSize('b', delta);
            } else {
                delta = isLeft ? (e.clientX - startX) : (startX - e.clientX);
                delta = startWidth + delta;
                if (isLeft)
                    this.wl = this.checkSize('l', delta);
                else
                    this.wr = this.checkSize('r', delta);
            }
        },
        stop: () => {
            document.removeEventListener('pointermove', this.#resizeHandler.move);
            document.removeEventListener('pointerup', this.#resizeHandler.stop);
            this.resizing = null;
        }
    }
}
customElements.define('fx-main', FxMain)
